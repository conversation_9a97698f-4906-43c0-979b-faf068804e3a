"use client";

import { useEffect } from 'react';
import ClientOnly from './ClientOnly';

/**
 * Component to block unwanted tracking scripts and clean up console errors
 * Specifically targets Facebook tracking and other third-party analytics
 */
export default function ScriptBlocker() {
  return (
    <ClientOnly>
      <ScriptBlockerContent />
    </ClientOnly>
  );
}

function ScriptBlockerContent() {
  useEffect(() => {
    // Block Facebook tracking scripts
    const blockFacebookTracking = () => {
      // Override Facebook tracking functions if they exist
      if (typeof window !== 'undefined') {
        // Block Facebook Pixel - silent version
        window.fbq = window.fbq || function() {
          // Silent block - no console logs to avoid clutter
        };

        // Block Facebook SDK - silent version
        window.FB = window.FB || {
          init: () => {},
          AppEvents: {
            logEvent: () => {},
            logPageView: () => {}
          }
        };

        // Block other common tracking - silent version
        window.gtag = window.gtag || function() {
          // Silent block
        };

        window.ga = window.ga || function() {
          // Silent block
        };

        // Block additional tracking methods
        window.dataLayer = window.dataLayer || [];
      }
    };

    // Block unwanted network requests (more targeted approach)
    const blockUnwantedRequests = () => {
      if (!window.fetch) return; // Skip if fetch is not available

      const originalFetch = window.fetch;
      window.fetch = function(input, init) {
        try {
          const url = typeof input === 'string' ? input : (input && input.url) || '';

          // Block specific tracking endpoints
          const blockedPaths = [
            'connect.facebook.net/en_US/fbevents.js',
            'connect.facebook.net/signals/config',
            'facebook.com/tr/',
            'facebook.com/plugins/like.php',
            'doubleclick.net',
            'google-analytics.com/collect',
            'google-analytics.com/g/collect',
            'googletagmanager.com/gtm.js',
            'googleadservices.com',
            'googlesyndication.com',
            'adsystem.com'
          ];

          if (url && blockedPaths.some(path => url.includes(path))) {
            // Return a resolved promise to avoid console errors
            return Promise.resolve(new Response('', { status: 204, statusText: 'Blocked by ScriptBlocker' }));
          }

          return originalFetch.call(this, input, init);
        } catch (error) {
          // Fallback to original fetch if there's any error
          return originalFetch.call(this, input, init);
        }
      };
    };

    // Clean up console errors by overriding console.error for specific patterns
    const cleanConsoleErrors = () => {
      const originalError = console.error;
      const originalWarn = console.warn;
      const originalLog = console.log;

      console.error = function(...args) {
        const message = args.join(' ');

        // Skip tracking-related errors (comprehensive list)
        if (
          message.includes('connect.facebook.net') ||
          message.includes('ERR_BLOCKED_BY_CLIENT') ||
          message.includes('ERR_NETWORK_CHANGED') ||
          message.includes('ERR_INTERNET_DISCONNECTED') ||
          message.includes('facebook') ||
          message.includes('fbevents') ||
          message.includes('doubleclick.net') ||
          message.includes('google-analytics.com') ||
          message.includes('googletagmanager.com') ||
          message.includes('googleadservices.com') ||
          message.includes('googlesyndication.com') ||
          message.includes('adsystem.com') ||
          message.includes('Failed to fetch') && (
            message.includes('facebook.com') ||
            message.includes('doubleclick.net') ||
            message.includes('google-analytics.com') ||
            message.includes('googleadservices.com')
          ) ||
          message.includes('net::ERR_BLOCKED_BY_CLIENT') ||
          message.includes('Tracking request blocked by ScriptBlocker') ||
          message.includes('Content Security Policy') ||
          message.includes('CSP') ||
          message.includes('Refused to load') ||
          message.includes('violates the following Content Security Policy directive') ||
          message.includes('script-src') ||
          message.includes('connect-src') ||
          message.includes('img-src') ||
          // Additional patterns from the screenshot
          message.includes('https://connect.facebook.net/en_US/fbevents.js') ||
          message.includes('net::ERR_BLOCKED_BY_CLIENT') ||
          message.includes('Uncaught (in promise)') && message.includes('facebook') ||
          message.includes('TypeError') && message.includes('facebook') ||
          message.includes('ReferenceError') && message.includes('facebook') ||
          message.includes('SyntaxError') && message.includes('facebook')
        ) {
          return; // Don't log these errors
        }

        // Log other errors normally
        originalError.apply(console, args);
      };

      console.warn = function(...args) {
        const message = args.join(' ');

        // Skip tracking-related warnings
        if (
          message.includes('connect.facebook.net') ||
          message.includes('facebook') ||
          message.includes('fbevents') ||
          message.includes('doubleclick.net') ||
          message.includes('google-analytics.com') ||
          message.includes('googletagmanager.com') ||
          message.includes('googleadservices.com') ||
          message.includes('googlesyndication.com') ||
          message.includes('adsystem.com') ||
          message.includes('ERR_BLOCKED_BY_CLIENT') ||
          message.includes('Tracking request blocked')
        ) {
          return; // Don't log these warnings
        }

        // Log other warnings normally
        originalWarn.apply(console, args);
      };
    };

    // Simple script blocking using MutationObserver
    const blockScriptInjection = () => {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;

              // Remove tracking scripts
              if (element.tagName === 'SCRIPT') {
                const src = element.getAttribute('src');
                if (src && (
                  src.includes('connect.facebook.net') ||
                  src.includes('doubleclick.net') ||
                  src.includes('google-analytics.com') ||
                  src.includes('googletagmanager.com')
                )) {
                  element.remove();
                  console.log('🚫 Removed tracking script:', src);
                }
              }

              // Check for scripts within added elements
              const scripts = element.querySelectorAll('script[src]');
              scripts.forEach((script) => {
                const src = script.getAttribute('src');
                if (src && (
                  src.includes('connect.facebook.net') ||
                  src.includes('doubleclick.net') ||
                  src.includes('google-analytics.com') ||
                  src.includes('googletagmanager.com')
                )) {
                  script.remove();
                  console.log('🚫 Removed nested tracking script:', src);
                }
              });
            }
          });
        });
      });

      observer.observe(document.documentElement, {
        childList: true,
        subtree: true
      });

      return observer;
    };

    // Initialize all blocking mechanisms
    blockFacebookTracking();
    blockUnwantedRequests();
    const scriptObserver = blockScriptInjection();
    cleanConsoleErrors();

    // Clean up on unmount
    return () => {
      // Disconnect the mutation observer
      if (scriptObserver) {
        scriptObserver.disconnect();
      }
      // Note: We don't restore original functions as this component should stay mounted
    };
  }, []);

  // This component doesn't render anything
  return null;
}
