"use client";

import { useEffect } from 'react';
import ClientOnly from './ClientOnly';

/**
 * Component to block unwanted tracking scripts and clean up console errors
 * Specifically targets Facebook tracking and other third-party analytics
 */
export default function ScriptBlocker() {
  return (
    <ClientOnly>
      <ScriptBlockerContent />
    </ClientOnly>
  );
}

function ScriptBlockerContent() {
  useEffect(() => {
    // Block Facebook tracking scripts
    const blockFacebookTracking = () => {
      // Override Facebook tracking functions if they exist
      if (typeof window !== 'undefined') {
        // Block Facebook Pixel
        window.fbq = window.fbq || function() {
          console.log('🚫 Facebook Pixel blocked');
        };
        
        // Block Facebook SDK
        window.FB = window.FB || {
          init: () => console.log('🚫 Facebook SDK blocked'),
          AppEvents: {
            logEvent: () => console.log('🚫 Facebook AppEvents blocked'),
            logPageView: () => console.log('🚫 Facebook PageView blocked')
          }
        };

        // Block other common tracking
        window.gtag = window.gtag || function() {
          console.log('🚫 Google Analytics blocked');
        };
        
        window.ga = window.ga || function() {
          console.log('🚫 Google Analytics (legacy) blocked');
        };
      }
    };

    // Block unwanted network requests (more targeted approach)
    const blockUnwantedRequests = () => {
      // Override fetch to block specific Facebook tracking requests
      const originalFetch = window.fetch;
      window.fetch = function(input, init) {
        const url = typeof input === 'string' ? input : input.url;

        // Block specific tracking endpoints
        const blockedPaths = [
          'connect.facebook.net/en_US/fbevents.js',
          'connect.facebook.net/signals/config',
          'facebook.com/tr/',
          'facebook.com/plugins/like.php',
          'doubleclick.net',
          'google-analytics.com/collect',
          'google-analytics.com/g/collect',
          'googletagmanager.com/gtm.js',
          'googleadservices.com',
          'googlesyndication.com',
          'adsystem.com'
        ];

        if (blockedPaths.some(path => url.includes(path))) {
          console.log(`🚫 Blocked tracking request to: ${url}`);
          return Promise.reject(new Error('Tracking request blocked by ScriptBlocker'));
        }

        return originalFetch.call(this, input, init);
      };
    };

    // Clean up console errors by overriding console.error for specific patterns
    const cleanConsoleErrors = () => {
      const originalError = console.error;
      const originalWarn = console.warn;

      console.error = function(...args) {
        const message = args.join(' ');

        // Skip tracking-related errors
        if (
          message.includes('connect.facebook.net') ||
          message.includes('ERR_BLOCKED_BY_CLIENT') ||
          message.includes('ERR_NETWORK_CHANGED') ||
          message.includes('ERR_INTERNET_DISCONNECTED') ||
          message.includes('facebook') ||
          message.includes('fbevents') ||
          message.includes('doubleclick.net') ||
          message.includes('google-analytics.com') ||
          message.includes('googletagmanager.com') ||
          message.includes('googleadservices.com') ||
          message.includes('googlesyndication.com') ||
          message.includes('adsystem.com') ||
          message.includes('Failed to fetch') && (
            message.includes('facebook.com') ||
            message.includes('doubleclick.net') ||
            message.includes('google-analytics.com') ||
            message.includes('googleadservices.com')
          ) ||
          message.includes('net::ERR_BLOCKED_BY_CLIENT') ||
          message.includes('Tracking request blocked by ScriptBlocker')
        ) {
          return; // Don't log these errors
        }

        // Log other errors normally
        originalError.apply(console, args);
      };

      console.warn = function(...args) {
        const message = args.join(' ');

        // Skip tracking-related warnings
        if (
          message.includes('connect.facebook.net') ||
          message.includes('facebook') ||
          message.includes('fbevents') ||
          message.includes('doubleclick.net') ||
          message.includes('google-analytics.com') ||
          message.includes('googletagmanager.com') ||
          message.includes('googleadservices.com') ||
          message.includes('googlesyndication.com') ||
          message.includes('adsystem.com') ||
          message.includes('ERR_BLOCKED_BY_CLIENT') ||
          message.includes('Tracking request blocked')
        ) {
          return; // Don't log these warnings
        }

        // Log other warnings normally
        originalWarn.apply(console, args);
      };
    };

    // Initialize all blocking mechanisms
    blockFacebookTracking();
    blockUnwantedRequests();
    cleanConsoleErrors();

    // Clean up on unmount
    return () => {
      // Restore original functions if needed
      // (In practice, this component should stay mounted)
    };
  }, []);

  // This component doesn't render anything
  return null;
}
