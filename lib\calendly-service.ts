"use client";

declare global {
  interface Window {
    Calendly?: {
      initInlineWidget: (options: {
        url: string;
        parentElement: HTMLElement | null;
        prefill?: Record<string, unknown>;
        utm?: Record<string, unknown>;
      }) => void;
    };
    calendlyScriptLoaded?: boolean;
    calendlyScriptLoading?: boolean;
  }
}

export interface CalendlyOptions {
  url?: string;
  parentElement: HTMLElement | null;
  prefill?: Record<string, unknown>;
  utm?: Record<string, unknown>;
}

class CalendlyService {
  private static instance: CalendlyService;
  private scriptLoaded = false;
  private scriptLoading = false;
  private loadPromise: Promise<void> | null = null;
  private readonly CALENDLY_URL = 'https://calendly.com/pilybas-edgaras/30min';
  private readonly SCRIPT_SRC = 'https://assets.calendly.com/assets/external/widget.js';

  private constructor() {}

  public static getInstance(): CalendlyService {
    if (!CalendlyService.instance) {
      CalendlyService.instance = new CalendlyService();
    }
    return CalendlyService.instance;
  }

  public async loadScript(): Promise<void> {
    // Return existing promise if already loading
    if (this.loadPromise) {
      return this.loadPromise;
    }

    // Return immediately if already loaded
    if (this.scriptLoaded || window.Calendly) {
      return Promise.resolve();
    }

    // Check if script is already in DOM
    const existingScript = document.querySelector(`script[src="${this.SCRIPT_SRC}"]`);
    if (existingScript) {
      this.loadPromise = new Promise((resolve, reject) => {
        existingScript.addEventListener('load', () => {
          this.scriptLoaded = true;
          resolve();
        });
        existingScript.addEventListener('error', reject);
      });
      return this.loadPromise;
    }

    // Load the script
    this.loadPromise = new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = this.SCRIPT_SRC;
      script.async = true;
      script.id = 'calendly-script';

      script.onload = () => {
        this.scriptLoaded = true;
        this.scriptLoading = false;
        console.log('✅ Calendly script loaded successfully');
        resolve();
      };

      script.onerror = (error) => {
        this.scriptLoading = false;
        this.loadPromise = null;
        console.error('❌ Failed to load Calendly script:', error);
        reject(error);
      };

      this.scriptLoading = true;
      document.head.appendChild(script);
    });

    return this.loadPromise;
  }

  public async initializeWidget(options: Partial<CalendlyOptions> = {}): Promise<void> {
    const { parentElement, url = this.CALENDLY_URL, prefill = {}, utm = {} } = options;

    if (!parentElement) {
      throw new Error('Parent element is required for Calendly widget initialization');
    }

    try {
      // Ensure script is loaded
      await this.loadScript();

      // Wait a bit for Calendly to be available
      if (!window.Calendly) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      if (!window.Calendly) {
        throw new Error('Calendly script loaded but Calendly object not available');
      }

      // Clear any existing content
      parentElement.innerHTML = '';

      // Initialize the widget
      window.Calendly.initInlineWidget({
        url,
        parentElement,
        prefill,
        utm
      });

      console.log('✅ Calendly widget initialized successfully');
    } catch (error) {
      console.error('❌ Calendly widget initialization failed:', error);
      
      // Show fallback content
      parentElement.innerHTML = `
        <div class="flex flex-col items-center justify-center h-full w-full text-center p-8">
          <p class="text-red-400 mb-4">Unable to load calendar</p>
          <p class="text-purple-100/70 text-sm mb-4">Please book directly:</p>
          <a href="${url}" target="_blank" rel="noopener noreferrer"
             class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
            Open Calendar in New Tab
          </a>
        </div>
      `;
      
      throw error;
    }
  }

  public showLoadingState(parentElement: HTMLElement): void {
    parentElement.innerHTML = `
      <div class="flex flex-col items-center justify-center h-full w-full text-center p-8">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mb-4"></div>
        <p class="text-purple-100/70 mb-4">Loading calendar...</p>
      </div>
    `;
  }

  public isScriptLoaded(): boolean {
    return this.scriptLoaded || !!window.Calendly;
  }

  public isScriptLoading(): boolean {
    return this.scriptLoading;
  }
}

export const calendlyService = CalendlyService.getInstance();
