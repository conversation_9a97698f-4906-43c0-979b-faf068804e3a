"use client"

import React, { useId, useEffect } from "react"
import { motion } from "framer-motion"

interface GradientBorderProps {
  width: number
  height?: number
  baseColor?: string
  gradientColors?: [string, string, string]
  animationDuration?: number
  strokeWidth?: number
}

export const GradientBorder: React.FC<GradientBorderProps> = ({
  width,
  height = 1,
  baseColor = "#E5E7EB",
  gradientColors = ["#9E00FF", "#E879F9", "#9E00FF"],
  animationDuration = 100, // Even slower default animation
  strokeWidth = 2,
}) => {
  // Ensure width is a valid number
  const safeWidth = typeof width === 'number' && !isNaN(width) ? width : 1000;
  const gradientId = `border-pulse-${useId()}`
  const path = `M0,${height / 2} L${safeWidth},${height / 2}`

  return (
    <div className="w-full relative" style={{ height }}>
      <svg
        width="100%"
        height={height}
        viewBox={`0 0 ${safeWidth} ${height}`}
        fill="none"
        preserveAspectRatio="none"
        className="absolute inset-0"
      >
        <path
          d={path}
          stroke={gradientColors[1]}
          strokeLinecap="round"
          strokeWidth={strokeWidth}
          opacity="0.3"
        />
      </svg>
      <svg
        width="100%"
        height={height}
        viewBox={`0 0 ${safeWidth} ${height}`}
        fill="none"
        preserveAspectRatio="none"
        className="absolute inset-0"
      >
        <path
          d={path}
          stroke={gradientColors[1]}
          strokeLinecap="round"
          strokeWidth={strokeWidth}
          strokeDasharray={`${safeWidth * 0.3} ${safeWidth * 0.7}`}
          style={{
            animationName: 'dash-flow',
            animationDuration: `${animationDuration}s`,
            animationTimingFunction: 'linear',
            animationIterationCount: 'infinite',
            strokeDashoffset: `${safeWidth * 2}px`
          }}
        />
      </svg>
      <style jsx global>{`
        @keyframes dash-flow {
          0% {
            stroke-dashoffset: ${safeWidth * 2}px;
          }
          100% {
            stroke-dashoffset: 0px;
          }
        }
      `}</style>
    </div>
  )
}
